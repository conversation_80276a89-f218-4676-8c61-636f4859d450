{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["CompanyModel", "ClinicalOutcomeRecord"], "actions": ["view"]}, {"resources": ["OutcomeConf", "ProductModel", "PriceListingModel", "CompanySubContractModel", "CompanyProductPriceListingModel", "CompanyContractModel", "ProductPriceListingModel"], "actions": ["view", "count"]}, {"resources": ["ConsolidatedHRCompanyReport"], "actions": ["view", "create", "update"]}, {"resources": ["PersonModel", "MemberModel", "BillingAccountablePartyModel", "Beneficiary<PERSON><PERSON>l", "PersonOnboardingModel", "PersonRegistrationModel", "PersonBillingAccountablePartyModel", "HealthDeclaration"], "actions": ["view", "create", "update", "count"]}, {"resources": ["CompanyScoreMagenta", "HrMemberUploadTracking"], "actions": ["view", "create", "update", "delete"]}]}], "aggregate": ["Beneficiary<PERSON><PERSON>l", "BillingAccountablePartyModel", "ClinicalOutcomeRecord", "CompanyContractModel", "CompanyModel", "CompanyProductPriceListingModel", "CompanyScoreMagenta", "CompanySubContractModel", "ConsolidatedHRCompanyReport", "HealthDeclaration", "HrMemberUploadTracking", "MemberModel", "OutcomeConf", "PersonModel", "PriceListingModel", "ProductModel", "ProductPriceListingModel"]}