package br.com.alice.channel.assistance.services.internal

import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.services.internal.ChannelNotificationService
import br.com.alice.channel.core.services.internal.ChannelService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.core.setChannel
import br.com.alice.channel.core.util.AI_STAFF_ID
import br.com.alice.channel.extensions.toLocalDateTime
import br.com.alice.channel.extensions.toTimestamp
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.notifier.ChannelArchivedByInactivityEvent
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.ChannelArchivedReason
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.services.NaiveTextualDeIdentificationService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.google.cloud.Timestamp
import java.time.Duration
import java.time.LocalDateTime

class LongitudinalChannelsInactivityService(
    private val channelFirestoreService: ChannelFirestoreService,
    private val channelService: ChannelService,
    private val messageFirestoreService: MessageFirestoreService,
    private val personService: PersonService,
    private val channelNotificationService: ChannelNotificationService
) : Spannable {

    @FirestoreContextUsage
    suspend fun process() {
        span("process") {
            sendEducationalMessageOrArchive(getInactivityBaseDates())
        }
    }

    @FirestoreContextUsage
    private suspend fun sendEducationalMessageOrArchive(configs: BaseInactivityConfigurations) {
        getInactiveLongitudinalChats(configs).mapEach { (channel, person) ->
            span("sendEducationalMessageOrArchive") { span ->
                span.setChannel(channel)

                val alreadySent = channel.messageAlreadySent(getEducationalMessage(person))
                if (alreadySent && channel.matchConfig(configs.archive))
                    archive(channel)
                else if (!alreadySent && channel.matchConfig(configs.sendEducationalMessage))
                    sendMessage(channel.id!!, getEducationalMessage(person))
            }
        }
    }

    suspend fun getInactiveLongitudinalChats(configs: BaseInactivityConfigurations) =
        channelFirestoreService.find { collectionReference ->
            collectionReference
                .whereEqualTo(ChannelDocument::status.name, ChannelStatus.ACTIVE.name)
                .whereEqualTo(ChannelDocument::kind.name, ChannelKind.CHAT.name)
                .whereEqualTo(ChannelDocument::subCategory.name, ChannelSubCategory.LONGITUDINAL.name)
                .whereEqualTo(ChannelDocument::isWaiting.name, false)
                .whereLessThan(ChannelDocument::inactiveAt.name, configs.sendEducationalMessage.baseDate)
        }.flatMap { chats -> enrichWithPerson(chats) }

    private suspend fun enrichWithPerson(chats: List<ChannelDocument>) =
        personService.findByIds(chats.map { it.personId })
            .map { persons -> persons.associateBy { it.id } }
            .map { personMap ->
                chats.map { chat ->
                    chat to personMap.getValue(chat.personId.toPersonId())
                }
            }

    @FirestoreContextUsage
    private suspend fun archive(channel: ChannelDocument) {
        span("archive") { span ->
            span.setChannel(channel)

            if (canProcessInactiveChats())
                channelService.archiveChannel(
                    channelDocument = channel,
                    requesterStaffId = "",
                    archivedReason = ChannelArchivedReason.MEMBER_INACTIVE,
                    force = true
                ).recordResult(span)
                    .then { archivedChannel ->
                        channelNotificationService.produceGenericEvent(
                            ChannelArchivedByInactivityEvent(
                                channelId = archivedChannel.id!!,
                                personId = archivedChannel.personId.toPersonId(),
                                channel = channel
                            )
                        )
                    }
        }
    }

    @FirestoreContextUsage
    private suspend fun sendMessage(channelId: String, content: String) {
        span("sendMessage") { span ->
            span.setAttribute("channel_id", channelId)

            if (canProcessInactiveChats())
                messageFirestoreService.add(
                    channelId,
                    MessageDocument(
                        userId = AI_STAFF_ID,
                        content = content,
                        type = MessageType.TEXT,
                    )
                ).recordResult(span)
        }
    }

    private fun getInactivityBaseDates() =
        LocalDateTime.now().let { baseDate ->
            FeatureService.getList(
                namespace = FeatureNamespace.CHANNELS,
                key = "truncated_longitudinal_chat_config_time_in_minutes",
                defaultValue = listOf(TWO_DAYS_IN_MINUTES, FOUR_DAYS_IN_MINUTES)
            ).map { it.toLong() }
                .map { Config(baseDate.minusMinutes(it).toTimestamp(), it) }
                .let {
                    BaseInactivityConfigurations(
                        sendEducationalMessage = it[0],
                        archive = it[1]
                    )
                }
        }

    private fun getEducationalMessage(person: Person) =
        FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "educational_longitudinal_chat_message",
            defaultValue = ""
        ).ifEmpty {
            throw InvalidArgumentException(
                code = "invalid_educational_longitudinal_chat_message",
                message = "Invalid educational_longitudinal_chat_message"
            )
        }.let { NaiveTextualDeIdentificationService.identify(it, person.toPersonPII()) }

    private fun canProcessInactiveChats() =
        FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "can_process_longitudinal_inactive_chat",
            defaultValue = false
        )

    private fun ChannelDocument.matchConfig(config: Config) =
        inactiveAt.minutesDiff(config.baseDate) > 0

    private fun ChannelDocument.messageAlreadySent(messageContent: String) =
        lastPreviewableMessage?.content == messageContent

    private fun Timestamp?.minutesDiff(from: Timestamp) =
        if (this == null) 0
        else Duration.between(this.toLocalDateTime(), from.toLocalDateTime()).toMinutes()

    data class BaseInactivityConfigurations(
        val sendEducationalMessage: Config,
        val archive: Config
    )

    data class Config(
        val baseDate: Timestamp,
        val minutes: Long
    )

    private companion object {
        const val TWO_DAYS_IN_MINUTES = 2880
        const val FOUR_DAYS_IN_MINUTES = 5760
    }
}
