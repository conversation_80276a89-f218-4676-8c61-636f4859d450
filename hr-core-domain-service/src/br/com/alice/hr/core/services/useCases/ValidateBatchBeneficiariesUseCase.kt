package br.com.alice.hr.core.services.useCases

import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.Company
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import java.util.UUID

class ValidateBatchBeneficiariesUseCase(
    private val staticValidateBeneficiariesUseCase: StaticValidateBeneficiariesUseCase,
    private val dynamicValidateBeneficiariesUseCase: DynamicValidateBeneficiariesUseCase,
    private val addressValidateBeneficiariesUseCase: AddressValidateBeneficiariesUseCase,
): Spannable {
    suspend fun run(
        beneficiaryBatch: BeneficiaryBatchTransport,
        company: Company,
        productTitles: List<String>,
        subContractTitles: List<String>,
        relationTypeOptions: List<String>,
    ): Result<ValidationAndBeneficiaries, Throwable> = span("ValidateBeneficiaries") {
        val success = mutableListOf<UUID>()
        val errors = mutableListOf<BeneficiaryBatchValidationError>()

        return@span staticValidateBeneficiariesUseCase.run(beneficiaryBatch).flatMap { staticValidationResult ->
            success.addAll(staticValidationResult.success)
            errors.addAll(staticValidationResult.errors)

            if (staticValidationResult.success.isEmpty()) {
                return@flatMap ValidationAndBeneficiaries(
                    validation = BeneficiaryBatchValidation(
                        productTitleOptions = productTitles,
                        subContractTitleOptions = subContractTitles,
                        parentBeneficiaryRelationOptions = relationTypeOptions,
                        success = staticValidationResult.success,
                        errors = staticValidationResult.errors,
                    ),
                    beneficiaries = emptyList(),
                ).success()
            }

            dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationResult).flatMap dynamicValidation@{ dynamicValidationResult ->
                success.addAll(dynamicValidationResult.success)
                errors.addAll(dynamicValidationResult.errors)

                if (dynamicValidationResult.success.isEmpty()) {
                    return@dynamicValidation ValidationAndBeneficiaries(
                        validation = BeneficiaryBatchValidation(
                            productTitleOptions = productTitles,
                            subContractTitleOptions = subContractTitles,
                            parentBeneficiaryRelationOptions = relationTypeOptions,
                            success = dynamicValidationResult.success,
                            errors = dynamicValidationResult.errors,
                        ),
                        beneficiaries = emptyList(),
                    ).success()
                }

                addressValidateBeneficiariesUseCase.run(beneficiaryBatch).flatMap addressValidation@{ (addressValidationResult, beneficiaries) ->
                    success.addAll(addressValidationResult.success)
                    errors.addAll(addressValidationResult.errors)

                    if (addressValidationResult.success.isEmpty()) {
                        return@addressValidation ValidationAndBeneficiaries(
                            validation = BeneficiaryBatchValidation(
                                productTitleOptions = productTitles,
                                subContractTitleOptions = subContractTitles,
                                parentBeneficiaryRelationOptions = relationTypeOptions,
                                success = addressValidationResult.success,
                                errors = addressValidationResult.errors,
                            ),
                            beneficiaries = emptyList(),
                        ).success()
                    }

                    val validation = BeneficiaryBatchValidation(
                        productTitleOptions = productTitles,
                        subContractTitleOptions = subContractTitles,
                        parentBeneficiaryRelationOptions = relationTypeOptions,
                        success = filterSuccessWithoutErrors(success, errors),
                        errors = errors,
                    )

                    ValidationAndBeneficiaries(
                        validation = validation,
                        beneficiaries = filterBeneficiariesWithoutErrors(beneficiaries, errors),
                    ).success()
                }
            }
        }
    }

    private fun filterBeneficiariesWithoutErrors(
        beneficiaries: List<BeneficiaryBatchItemTransport>,
        errors: List<BeneficiaryBatchValidationError>,
    ): List<BeneficiaryBatchItemTransport> {
        val indexesWithErrors = errors.map { it.index }
        return beneficiaries.filter { it.index !in indexesWithErrors }.distinct()
    }

    private fun filterSuccessWithoutErrors(
        success: List<UUID>,
        errors: List<BeneficiaryBatchValidationError>,
    ): List<UUID> {
        val indexesWithErrors = errors.map { it.index }
        return success.filter { it !in indexesWithErrors }.distinct()
    }
}

data class ValidationAndBeneficiaries(
    val validation: BeneficiaryBatchValidation,
    val beneficiaries: List<BeneficiaryBatchItemTransport>,
)
