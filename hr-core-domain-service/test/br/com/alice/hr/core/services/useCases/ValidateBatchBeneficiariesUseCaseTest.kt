package br.com.alice.hr.core.services.useCases

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyAddress
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class ValidateBatchBeneficiariesUseCaseTest {
    private val staticValidateBeneficiariesUseCase = mockk<StaticValidateBeneficiariesUseCase>()
    private val dynamicValidateBeneficiariesUseCase = mockk<DynamicValidateBeneficiariesUseCase>()
    private val addressValidateBeneficiariesUseCase = mockk<AddressValidateBeneficiariesUseCase>()

    private val validateBatchBeneficiariesUseCase = ValidateBatchBeneficiariesUseCase(
        staticValidateBeneficiariesUseCase,
        dynamicValidateBeneficiariesUseCase,
        addressValidateBeneficiariesUseCase
    )

    private val uploadId = RangeUUID.generate()
    private val companyId = RangeUUID.generate()
    private val index1 = RangeUUID.generate()
    private val index2 = RangeUUID.generate()
    private val today = LocalDate.now().toBrazilianDateFormat()

    private val company = Company(
        id = companyId,
        name = "Test Company",
        legalName = "Test Company LTDA",
        cnpj = "12345678000195",
        email = "<EMAIL>",
        phoneNumber = "11999999999",
        address = CompanyAddress(
            postalCode = "12345678",
            street = "Test Street",
            number = 123,
            city = "Test City",
            State = "SP"
        ),
        contractStartedAt = LocalDateTime.now().minusDays(30)
    )

    private val beneficiaryItem1 = BeneficiaryBatchItemTransport(
        index = index1,
        nationalId = "12345678901",
        fullName = "João Silva",
        email = "<EMAIL>",
        ownership = "Titular",
        activatedAt = today,
        beneficiaryContractType = "CLT",
        productTitle = "Plano Básico",
        subContractTitle = "Matriz",
        addressPostalCode = "12345678"
    )

    private val beneficiaryItem2 = BeneficiaryBatchItemTransport(
        index = index2,
        nationalId = "98765432109",
        fullName = "Maria Santos",
        email = "<EMAIL>",
        ownership = "Dependente",
        activatedAt = today,
        parentNationalId = "12345678901",
        parentBeneficiaryRelationType = "Cônjuge",
        productTitle = "Plano Básico",
        subContractTitle = "Matriz",
        addressPostalCode = "87654321"
    )

    private val beneficiaryBatch = BeneficiaryBatchTransport(
        uploadId = uploadId,
        items = listOf(beneficiaryItem1, beneficiaryItem2)
    )

    private val productTitles = listOf("Plano Básico", "Plano Premium")
    private val subContractTitles = listOf("Matriz", "Filial")
    private val relationTypeOptions = listOf("Cônjuge", "Filho(a)")

    private val staticValidationSuccess = BeneficiaryBatchValidation(
        success = listOf(index1, index2),
        errors = emptyList()
    )

    private val dynamicValidationSuccess = BeneficiaryBatchValidation(
        success = listOf(index1, index2),
        errors = emptyList()
    )

    private val addressValidationSuccess = BeneficiaryBatchValidation(
        success = listOf(index1, index2),
        errors = emptyList()
    )

    private val processedBeneficiaries = listOf(
        beneficiaryItem1.copy(addressStreet = "Rua A"),
        beneficiaryItem2.copy(addressStreet = "Rua B")
    )

    @Nested
    inner class SuccessfulValidation {
        @Test
        fun `should return success when all validations pass`() = runBlocking {
            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(listOf(index1, index2), result.validation.success)
            assertEquals(emptyList(), result.validation.errors)
            assertEquals(processedBeneficiaries, result.beneficiaries)
            assertEquals(productTitles, result.validation.productTitleOptions)
            assertEquals(subContractTitles, result.validation.subContractTitleOptions)
            assertEquals(relationTypeOptions, result.validation.parentBeneficiaryRelationOptions)
        }

        @Test
        fun `should accumulate success from all validation steps`() = runBlocking {
            val staticSuccess = BeneficiaryBatchValidation(success = listOf(index1), errors = emptyList())
            val dynamicSuccess = BeneficiaryBatchValidation(success = listOf(index2), errors = emptyList())
            val addressSuccess = BeneficiaryBatchValidation(success = listOf(index1, index2), errors = emptyList())

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticSuccess) } returns dynamicSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(listOf(index1, index2), result.validation.success)
            assertEquals(processedBeneficiaries, result.beneficiaries)
        }
    }

    @Nested
    inner class StaticValidationFailure {
        @Test
        fun `should return early when static validation has no success`() = runBlocking {
            val staticError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val staticValidationFailure = BeneficiaryBatchValidation(
                success = emptyList(),
                errors = listOf(staticError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationFailure.success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.success)
            assertEquals(listOf(staticError), result.validation.errors)
            assertEquals(emptyList(), result.beneficiaries)
            assertEquals(productTitles, result.validation.productTitleOptions)
        }

        @Test
        fun `should accumulate errors when static validation has partial success`() = runBlocking {
            val staticError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("fullName", "Nome obrigatório"))
            )
            val staticValidationPartial = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(staticError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationPartial.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationPartial) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertTrue(result.validation.success.contains(index1))
            assertTrue(result.validation.errors.contains(staticError))
        }
    }

    @Nested
    inner class DynamicValidationFailure {
        @Test
        fun `should return early when dynamic validation has no success`() = runBlocking {
            val dynamicError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("productTitle", "Produto não encontrado"))
            )
            val dynamicValidationFailure = BeneficiaryBatchValidation(
                success = emptyList(),
                errors = listOf(dynamicError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationFailure.success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.success)
            assertEquals(listOf(dynamicError), result.validation.errors)
            assertEquals(emptyList(), result.beneficiaries)
        }

        @Test
        fun `should accumulate errors when dynamic validation has partial success`() = runBlocking {
            val dynamicError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("subContractTitle", "Subcontrato não encontrado"))
            )
            val dynamicValidationPartial = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(dynamicError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationPartial.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertTrue(result.validation.success.contains(index1))
            assertTrue(result.validation.errors.contains(dynamicError))
        }
    }

    @Nested
    inner class AddressValidationFailure {
        @Test
        fun `should return early when address validation has no success`() = runBlocking {
            val addressError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("addressPostalCode", "CEP inválido"))
            )
            val addressValidationFailure = BeneficiaryBatchValidation(
                success = emptyList(),
                errors = listOf(addressError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationFailure to emptyList<BeneficiaryBatchItemTransport>()).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.success)
            assertEquals(listOf(addressError), result.validation.errors)
            assertEquals(emptyList(), result.beneficiaries)
        }

        @Test
        fun `should accumulate errors when address validation has partial success`() = runBlocking {
            val addressError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("addressPostalCode", "CEP não encontrado"))
            )
            val addressValidationPartial = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(addressError)
            )
            val partialBeneficiaries = listOf(beneficiaryItem1.copy(addressStreet = "Rua A"))

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationPartial to partialBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertTrue(result.validation.success.contains(index1))
            assertTrue(result.validation.errors.contains(addressError))
            assertEquals(partialBeneficiaries, result.beneficiaries)
        }
    }

    @Nested
    inner class ErrorAccumulation {
        @Test
        fun `should accumulate errors from all validation steps`() = runBlocking {
            val staticError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val dynamicError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("productTitle", "Produto não encontrado"))
            )
            val addressError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("addressPostalCode", "CEP inválido"))
            )

            val staticValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(staticError)
            )
            val dynamicValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(dynamicError)
            )
            val addressValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(addressError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationWithError.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationWithError) } returns dynamicValidationWithError.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationWithError to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(3, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(staticError))
            assertTrue(result.validation.errors.contains(dynamicError))
            assertTrue(result.validation.errors.contains(addressError))
        }

        @Test
        fun `should accumulate success and errors from mixed validation results`() = runBlocking {
            val staticError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("email", "Email inválido"))
            )
            val dynamicError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("activatedAt", "Data inválida"))
            )

            val staticValidationMixed = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(staticError)
            )
            val dynamicValidationMixed = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(dynamicError)
            )
            val addressValidationMixed = BeneficiaryBatchValidation(
                success = listOf(index1, index2),
                errors = emptyList()
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationMixed.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationMixed) } returns dynamicValidationMixed.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationMixed to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.success)
            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(staticError))
            assertTrue(result.validation.errors.contains(dynamicError))
            assertEquals(emptyList(), result.beneficiaries)
        }
    }

    @Nested
    inner class EmptyBatch {
        @Test
        fun `should handle empty beneficiary batch`() = runBlocking {
            val emptyBatch = BeneficiaryBatchTransport(uploadId = uploadId, items = emptyList())
            val emptyValidation = BeneficiaryBatchValidation(success = emptyList(), errors = emptyList())

            coEvery { staticValidateBeneficiariesUseCase.run(emptyBatch) } returns emptyValidation.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, emptyBatch) } returns emptyValidation.success()
            coEvery { addressValidateBeneficiariesUseCase.run(emptyBatch) } returns (emptyValidation to emptyList<BeneficiaryBatchItemTransport>()).success()

            val result = validateBatchBeneficiariesUseCase.run(
                emptyBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.success)
            assertEquals(emptyList(), result.validation.errors)
            assertEquals(emptyList(), result.beneficiaries)
        }
    }

    @Nested
    inner class OptionsValidation {
        @Test
        fun `should include provided options in validation result`() = runBlocking {
            val customProductTitles = listOf("Custom Plan A", "Custom Plan B")
            val customSubContractTitles = listOf("Branch 1", "Branch 2")
            val customRelationTypeOptions = listOf("Spouse", "Child", "Parent")

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                customProductTitles,
                customSubContractTitles,
                customRelationTypeOptions
            ).get()

            assertEquals(customProductTitles, result.validation.productTitleOptions)
            assertEquals(customSubContractTitles, result.validation.subContractTitleOptions)
            assertEquals(customRelationTypeOptions, result.validation.parentBeneficiaryRelationOptions)
        }

        @Test
        fun `should handle empty options lists`() = runBlocking {
            val emptyOptions = emptyList<String>()

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                emptyOptions,
                emptyOptions,
                emptyOptions
            ).get()

            assertEquals(emptyOptions, result.validation.productTitleOptions)
            assertEquals(emptyOptions, result.validation.subContractTitleOptions)
            assertEquals(emptyOptions, result.validation.parentBeneficiaryRelationOptions)
        }
    }
}
