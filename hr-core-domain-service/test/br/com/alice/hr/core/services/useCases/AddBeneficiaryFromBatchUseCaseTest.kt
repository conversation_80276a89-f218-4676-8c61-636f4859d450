package br.com.alice.hr.core.services.useCases

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.HrMemberUploadTracking
import br.com.alice.data.layer.models.HrMemberUploadTrackingStatus
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.services.HrMemberUploadTrackingDataService
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class AddBeneficiaryFromBatchUseCaseTest {
    private val companyService: CompanyService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val personService: PersonService = mockk()
    private val productService: ProductService = mockk()
    private val hrMemberUploadTrackingDataService: HrMemberUploadTrackingDataService = mockk()
    private val dynamicValidateBeneficiariesUseCase: DynamicValidateBeneficiariesUseCase = mockk()

    private val useCase = AddBeneficiaryFromBatchUseCase(
        companyService = companyService,
        beneficiaryService = beneficiaryService,
        companySubContractService = companySubContractService,
        personService = personService,
        productService = productService,
        hrMemberUploadTrackingDataService = hrMemberUploadTrackingDataService,
        dynamicValidateBeneficiariesUseCase = dynamicValidateBeneficiariesUseCase
    )

    private val uploadId = RangeUUID.generate()
    private val companyId = RangeUUID.generate()
    private val nationalId = "12345678901"
    private val company = TestModelFactory.buildCompany(id = companyId)
    private val hrMemberUploadTracking = TestModelFactory.buildHrMemberUploadTracking(
        memberNationalId = nationalId,
        status = HrMemberUploadTrackingStatus.QUEUED
    )
    private val index = RangeUUID.generate()
    private val beneficiaryItem = BeneficiaryBatchItemTransport(
        index = index,
        nationalId = nationalId,
        fullName = "João da Silva",
        cnpj = null,
        subContractTitle = "Matriz",
        sex = "M",
        dateOfBirth = "01/01/1990",
        mothersName = "Maria da Silva",
        email = "<EMAIL>",
        phoneNumber = "11999999999",
        addressPostalCode = "01234567",
        addressStreet = "Rua Teste",
        addressCity = "São Paulo",
        addressState = "SP",
        addressNeighborhood = "Jardim Teste",
        addressNumber = "123",
        addressComplement = "Apto 1",
        productTitle = "Plano Básico",
        activatedAt = "01/01/2024",
        beneficiaryContractType = "CLT",
        hiredAt = "01/01/2024",
        parentNationalId = null,
        parentBeneficiaryRelationType = null,
        relationExceeds30Days = "nao",
        ownership = "Titular"
    )
    private val beneficiaryItemWithParent = beneficiaryItem.copy(
        index = index,
        nationalId = nationalId,
        fullName = "João da Silva",
        cnpj = null,
        subContractTitle = "Matriz",
        sex = "M",
        dateOfBirth = "01/01/1990",
        mothersName = "Maria da Silva",
        email = "<EMAIL>",
        phoneNumber = "11999999999",
        addressPostalCode = "01234567",
        addressStreet = "Rua Teste",
        addressCity = "São Paulo",
        addressState = "SP",
        addressNeighborhood = "Jardim Teste",
        addressNumber = "123",
        addressComplement = "Apto 1",
        productTitle = "Plano Básico",
        activatedAt = "01/01/2024",
        beneficiaryContractType = null,
        hiredAt = null,
        parentNationalId = "12345678902",
        parentBeneficiaryRelationType = "Filha",
        relationExceeds30Days = "nao",
        ownership = "Dependente"
    )
    private val beneficiaryBatch = BeneficiaryBatchTransport(
        uploadId = uploadId,
        items = listOf(beneficiaryItem)
    )
    private val beneficiaryBatchWithParent = BeneficiaryBatchTransport(
        uploadId = uploadId,
        items = listOf(beneficiaryItemWithParent)
    )
    private val validationResultSuccess = BeneficiaryBatchValidation(
        success = listOf(index),
        errors = emptyList()
    )
    private val validationResultWithError = BeneficiaryBatchValidation(
        success = emptyList(),
        errors = listOf(
            BeneficiaryBatchValidationError(
                index = index,
                error = listOf(
                    BeneficiaryBatchValidationErrorItem(
                        field = "nationalId",
                        message = "CPF inválido"
                    )
                )
            )
        )
    )
    private val subContract = TestModelFactory.buildCompanySubContract(title = "Matriz", companyId = companyId)
    private val product = TestModelFactory.buildProduct(title = "Plano Básico")
    private val beneficiary = TestModelFactory.buildBeneficiary(companyId = companyId)

    @Test
    fun `#run should create beneficiary successfully when all validations pass and tracking is QUEUED`() = runBlocking {
        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isSuccess()
        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }

    @Test
    fun `#run should create beneficiary successfully when all validations pass and tracking is QUEUED and beneficiary has one name`() = runBlocking {
        val beneficiaryItem = beneficiaryItem.copy(fullName = "João")
        val beneficiaryBatch = beneficiaryBatch.copy(items = listOf(beneficiaryItem))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isSuccess()
        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }

    @Test
    fun `#run should create beneficiary successfully when all validations pass and tracking is QUEUED and beneficiary is PJ`() = runBlocking {
        val beneficiaryItem = beneficiaryItem.copy(beneficiaryContractType = "PJ", cnpj = "12345678000195")
        val beneficiaryBatch = beneficiaryBatch.copy(items = listOf(beneficiaryItem))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isSuccess()
        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and parent is not null`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is daughter`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemDaughter = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "Filha", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemDaughter))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is spouse`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemSpouse = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "Cônjuge", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemSpouse))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is stepchild`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemStepChild = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "Enteado", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemStepChild))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is brother`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemBrother = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "Irmão", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemBrother))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is daughter in law`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemDaughterInLaw = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "nora", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemDaughterInLaw))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is brother in law`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemBrotherInLaw = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "genro", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemBrotherInLaw))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is greatgrandchild`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemGreatGrandchild = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "bisnet", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemGreatGrandchild))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is grandchild`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemGrandchild = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "net", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemGrandchild))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is father`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemFather = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "pai", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemFather))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is mother`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemMother = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "mãe", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemMother))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is niece`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemNiece = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "sobrinha", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemNiece))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is step father`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemStepFather = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "padrasto", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemStepFather))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is step mother`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemStepMother = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "madrasta", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemStepMother))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should create dependent beneficiary with parent successfully when all validations pass and relationship is grandfather`() = runBlocking {
        val parentPerson = TestModelFactory.buildPerson(nationalId = "12345678902")
        val parentBeneficiary = TestModelFactory.buildBeneficiary(companyId = companyId, personId = parentPerson.id)
        val beneficiaryItemGrandfather = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "avô", parentNationalId = "12345678902")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItemGrandfather))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary.success()
        coEvery { personService.findByNationalId("12345678902") } returns parentPerson.success()
        coEvery { beneficiaryService.findByCompanyIdAndPersonIds(companyId, listOf(parentPerson.id), any()) } returns listOf(parentBeneficiary).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { beneficiaryService.findByCompanyIdAndPersonIds(any(), any(), any()) }
    }

    @Test
    fun `#run should return error when dependent beneficiary relation type is invalid`() = runBlocking {
        val beneficiaryItem = beneficiaryItemWithParent.copy(parentBeneficiaryRelationType = "Invalid")
        val beneficiaryBatch = beneficiaryBatchWithParent.copy(items = listOf(beneficiaryItem))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
    }

    @Test
    fun `#run should fail when parent tracking status is not CREATED`() = runBlocking {
        val parentHrMemberUploadTracking = TestModelFactory.buildHrMemberUploadTracking(
            memberNationalId = "12345678902",
            status = HrMemberUploadTrackingStatus.ERROR
        )

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { hrMemberUploadTrackingDataService.findLastByMemberNationalId("12345678902") } returns parentHrMemberUploadTracking.success()
        coEvery { personService.findByNationalId("12345678902") } returns Exception("Person not found").failure()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findLastByMemberNationalId(any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
    }

    @Test
    fun `#run should fail when parent tracking status is CREATED`() = runBlocking {
        val parentHrMemberUploadTracking = TestModelFactory.buildHrMemberUploadTracking(
            memberNationalId = "12345678902",
            status = HrMemberUploadTrackingStatus.CREATED
        )

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { hrMemberUploadTrackingDataService.findLastByMemberNationalId("12345678902") } returns parentHrMemberUploadTracking.success()
        coEvery { personService.findByNationalId("12345678902") } returns Exception("Person not found").failure()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findLastByMemberNationalId(any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
    }

    @Test
    fun `#run should fail when dynamic validation returns error`() = runBlocking {
        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultWithError.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()
        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyNone { companySubContractService.findByTitles(any()) }
        coVerifyNone { productService.findActiveByTitles(any()) }
        coVerifyNone { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }

    @Test
    fun `#run should fail when hrMemberUploadTracking status is not QUEUED`() = runBlocking {
        val trackingNotQueued = hrMemberUploadTracking.copy(status = HrMemberUploadTrackingStatus.ERROR)
        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns trackingNotQueued.success()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()
        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyNone { companySubContractService.findByTitles(any()) }
        coVerifyNone { productService.findActiveByTitles(any()) }
        coVerifyNone { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }

    @Test
    fun `#run should fail when subContract returns empty list`() = runBlocking {

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns emptyList<CompanySubContract>().success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
    }

    @Test
    fun `#run should fail when product returns empty list`() = runBlocking {

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatchWithParent) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns emptyList<Product>().success()

        val result = useCase.run(companyId, beneficiaryItemWithParent, uploadId)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
    }

    @Test
    fun `#run should fail when ownership is not valid`() = runBlocking {
        val beneficiaryItem = beneficiaryItem.copy(ownership = "Invalid")
        val beneficiaryBatch = beneficiaryBatch.copy(items = listOf(beneficiaryItem))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
    }

    @Test
    fun `#run should fail when creating beneficiary function throws a null pointer exception`() = runBlocking {
        val beneficiaryItem = beneficiaryItem.copy(dateOfBirth = null)
        val beneficiaryBatch = beneficiaryBatch.copy(items = listOf(beneficiaryItem))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
    }

    @Test
    fun `#run should fail when company service returns error`() = runBlocking {
        coEvery { companyService.get(companyId) } returns RuntimeException("Erro ao buscar empresa").failure()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()
        coVerifyOnce { companyService.get(any()) }
        coVerifyNone { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyNone { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyNone { companySubContractService.findByTitles(any()) }
        coVerifyNone { productService.findActiveByTitles(any()) }
        coVerifyNone { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }

    @Test
    fun `#run should fail when hrMemberUploadTrackingDataService returns error`() = runBlocking {
        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns RuntimeException("Erro no tracking").failure()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()
        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyNone { companySubContractService.findByTitles(any()) }
        coVerifyNone { productService.findActiveByTitles(any()) }
        coVerifyNone { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }

    @Test
    fun `#run should fail when companySubContractService returns error`() = runBlocking {
        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns RuntimeException("Erro no subcontract").failure()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()
        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyNone { productService.findActiveByTitles(any()) }
        coVerifyNone { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }

    @Test
    fun `#run should fail when productService returns error`() = runBlocking {
        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns RuntimeException("Erro no product").failure()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()
        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
        coVerifyNone { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }

    @Test
    fun `#run should fail when beneficiaryService returns error`() = runBlocking {
        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns RuntimeException("Erro no businessService").failure()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()
        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
    }

    @Test
    fun `#run should return error when BeneficiaryTransport throws an exception`() = runBlocking {
        val beneficiaryItem = beneficiaryItem.copy(hiredAt = "01/2024/01", dateOfBirth = "01/2024/01")
        val beneficiaryBatch = beneficiaryBatch.copy(items = listOf(beneficiaryItem))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
    }

    @Test
    fun `#run should return error when sex in beneficiaryItem is invalid`() = runBlocking {
        val beneficiaryItem = beneficiaryItem.copy(sex = "invalid")
        val beneficiaryBatch = beneficiaryBatch.copy(items = listOf(beneficiaryItem))

        coEvery { companyService.get(companyId) } returns company.success()
        coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch) } returns validationResultSuccess.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(nationalId, uploadId) } returns hrMemberUploadTracking.success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(subContract).success()
        coEvery { productService.findActiveByTitles(listOf("Plano Básico")) } returns listOf(product).success()

        val result = useCase.run(companyId, beneficiaryItem, uploadId)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(any(), any()) }
        coVerifyOnce { companySubContractService.findByTitles(any()) }
        coVerifyOnce { productService.findActiveByTitles(any()) }
    }
}
