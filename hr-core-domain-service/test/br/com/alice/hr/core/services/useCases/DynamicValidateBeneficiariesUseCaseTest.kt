package br.com.alice.hr.core.services.useCases

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.HrMemberUploadTrackingStatus.ERROR
import br.com.alice.data.layer.models.HrMemberUploadTrackingStatus.QUEUED
import br.com.alice.data.layer.models.HrMemberUploadTrackingStatus.CREATED
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.services.HrMemberUploadTrackingDataService
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.assertFailsWith

class DynamicValidateBeneficiariesUseCaseTest {
    private val productService: ProductService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val personService: PersonService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val companyProductPriceListingService: CompanyProductPriceListingService = mockk()
    private val hrMemberUploadTrackingDataService: HrMemberUploadTrackingDataService = mockk()
    private val memberService: MemberService = mockk()

    private val dynamicValidateBeneficiariesUseCase = DynamicValidateBeneficiariesUseCase(
        productService,
        beneficiaryService,
        personService,
        companySubContractService,
        companyProductPriceListingService,
        hrMemberUploadTrackingDataService,
        memberService,
    )

    private val today = LocalDate.now().minusDays(5).toBrazilianDateFormat()
    private val companyId = RangeUUID.generate()
    private val localDateTime = LocalDateTime.now()
    private val index1 = RangeUUID.generate()
    private val index2 = RangeUUID.generate()
    private val beneficiaryBatchItemTransportValid = BeneficiaryBatchItemTransport(
        index = index1,
        nationalId = "438.595.570-09",
        dateOfBirth = "01/01/1990",
        fullName = "Calango Zokas",
        sex = "M",
        cnpj = null,
        subContractTitle = "Matriz",
        phoneNumber = "1191234-5678",
        mothersName = "Jane Doe",
        email = "<EMAIL>",
        addressNumber = "222",
        addressPostalCode = "12345678",
        addressComplement = null,
        productTitle = "Plano de Saúde Bonzao",
        activatedAt = today,
        beneficiaryContractType = "CLT",
        hiredAt = "01/02/2023",
        parentNationalId = null,
        parentBeneficiaryRelationType = null,
        relationExceeds30Days = "nao",
        ownership = "Titular",
    )
    private val dependentBeneficiaryBatchItemTransportValid = beneficiaryBatchItemTransportValid.copy(
        index = index2,
        ownership = "Dependente",
        parentNationalId = "438.595.570-09",
        parentBeneficiaryRelationType = "Cônjuge",
        relationExceeds30Days = "sim",
    )
    private val beneficiaryBatchTransport = BeneficiaryBatchTransport(
        items = emptyList(),
        uploadId = RangeUUID.generate(),
    )
    private val company = TestModelFactory.buildCompany(
        id = companyId,
        contractStartedAt = localDateTime.minusDays(30),
    )
    private val product = TestModelFactory.buildProduct(
        title = "Plano de Saúde Bonzao",
    )
    private val person = TestModelFactory.buildPerson()
    private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(
        nationalId = "75.006.845/0001-93"
    )
    private val companySubContract = TestModelFactory.buildCompanySubContract(
        title = "Matriz",
        companyId = companyId,
        billingAccountablePartyId = billingAccountableParty.id,
        availableProducts = listOf(product.id),
    )
    private val companyProductPriceListing = TestModelFactory.buildCompanyProductPriceListing(
        productId = product.id,
        companySubContractId = companySubContract.id
    ).copy(isBlockedForSale = false)

    @Test
    fun `run should return empty validation when no items`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = emptyList()
        )

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(BeneficiaryBatchValidation(), result)
    }

    @Test
    fun `run should validate product exists`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery {
            productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!))
        } returns emptyList<Product>().success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto não encontrado.",
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate return error when holder does not exist`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns NotFoundException().failure()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009") } returns NotFoundException().failure()
        
        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        
        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )
        
        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should return success when all validations pass for PJ`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid.copy(
                cnpj = "75.006.845/0001-93",
                beneficiaryContractType = "PJ",
            ))
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should return success when all validations pass for CLT`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should validate dependent activation date is after holder hired date`() = runBlocking {
        val holderHiredDate = localDateTime.plusDays(5)
        val dependentActivationDate = localDateTime.toLocalDate().minusDays(10).toBrazilianDateFormat()

        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = holderHiredDate,
            activatedAt = localDateTime.minusDays(10),
        )
        val dependentItem = dependentBeneficiaryBatchItemTransportValid.copy(
            activatedAt = dependentActivationDate,
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentItem),
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(dependentItem.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentItem.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "activatedAt",
                    message = "Data de ativação deve ser igual ou posterior à contratação do titular."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate dependent activation date is after holder activation date`() = runBlocking {
        val holderActivationDate = localDateTime.plusDays(5)
        val dependentActivationDate = localDateTime.toLocalDate().minusDays(2).toBrazilianDateFormat()

        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            parentBeneficiary = null,
            parentBeneficiaryRelationType = null,
            hiredAt = LocalDateTime.of(2023, 1, 1, 0, 0),
            activatedAt = holderActivationDate
        )
        val dependentItem = dependentBeneficiaryBatchItemTransportValid.copy(
            activatedAt = dependentActivationDate
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentItem)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(dependentItem.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentItem.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "activatedAt",
                    message = "Data de ativação do dependente deve ser igual ou posterior à data de ativação do titular."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate dependent activation date is after holder activation date from holder in batch`() = runBlocking {
        val holderActivationDate = localDateTime.plusDays(5)
        val dependentActivationDate = localDateTime.toLocalDate().minusDays(2).toBrazilianDateFormat()
        val holderInBatch = beneficiaryBatchItemTransportValid.copy(
            index = index1,
            activatedAt = holderActivationDate.toBrazilianDateFormat(),
        )
        val dependentItem = dependentBeneficiaryBatchItemTransportValid.copy(
            activatedAt = dependentActivationDate
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentItem, holderInBatch)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(dependentItem.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(holderInBatch.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(holderInBatch.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns NotFoundException().failure()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "activatedAt",
                    message = "Data de ativação do dependente deve ser igual ou posterior à data de ativação do titular."
                )
            )
        )

        assertEquals(listOf(index1), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle multiple beneficiaries with mixed validation results`() = runBlocking {
        val validBeneficiary = beneficiaryBatchItemTransportValid.copy(index = index1)
        val validDependent = dependentBeneficiaryBatchItemTransportValid.copy(index = index2)

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(validBeneficiary, validDependent)
        )

        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(validBeneficiary.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(validBeneficiary.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1, index2), result.success)
        assertEquals(0, result.errors.size)
    }

    @Test
    fun `run should handle beneficiary with both product and holder validation errors`() = runBlocking {
        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.plusDays(5),
            activatedAt = localDateTime.minusDays(10)
        )
        val dependentItem = dependentBeneficiaryBatchItemTransportValid.copy(
            productTitle = "Produto Inexistente",
            activatedAt = LocalDate.now().minusDays(2).toBrazilianDateFormat()
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentItem)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(dependentItem.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentItem.productTitle!!)) } returns emptyList<Product>().success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedProductError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto não encontrado."
                )
            )
        )

        val expectedHolderError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "activatedAt",
                    message = "Data de ativação deve ser igual ou posterior à contratação do titular."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(2, result.errors.size)
        assertEquals(expectedProductError, result.errors[0])
        assertEquals(expectedHolderError, result.errors[1])
    }

    @Test
    fun `run should skip holder validation when parentNationalId is null`() = runBlocking {
        val holderItem = beneficiaryBatchItemTransportValid.copy(
            parentNationalId = null
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(holderItem)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle successful dependent validation with valid holder`() = runBlocking {
        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle product service returning null`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns NotFoundException().failure()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto não encontrado."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle multiple products with some found and some not found`() = runBlocking {
        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
            index = index1,
            productTitle = "Plano de Saúde Bonzao"
        )
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
            index = index2,
            productTitle = "Produto Inexistente"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiary1.productTitle!!, beneficiary2.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto não encontrado."
                )
            )
        )

        assertEquals(listOf(index1), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle large batch with multiple validation scenarios`() = runBlocking {
        val index3 = RangeUUID.generate()
        val index4 = RangeUUID.generate()
        val index5 = RangeUUID.generate()
        val validHolder = beneficiaryBatchItemTransportValid.copy(index = index1)
        val invalidProduct = beneficiaryBatchItemTransportValid.copy(
            index = index2,
            productTitle = "Produto Inexistente"
        )
        val validDependent = dependentBeneficiaryBatchItemTransportValid.copy(index = index3)
        val invalidDateDependent = dependentBeneficiaryBatchItemTransportValid.copy(
            index = index4,
            activatedAt = localDateTime.minusDays(2).toBrazilianDateFormat()
        )
        val anotherValidHolder = beneficiaryBatchItemTransportValid.copy(
            index = index5,
            nationalId = "123.456.789-00"
        )

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(validHolder, invalidProduct, validDependent, invalidDateDependent, anotherValidHolder)
        )

        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val invalidDateHolderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.plusDays(5),
            activatedAt = localDateTime.minusDays(10)
        )

        coEvery { personService.findByNationalId(validHolder.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(invalidProduct.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(validDependent.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(invalidDateDependent.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(anotherValidHolder.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(validHolder.productTitle!!, invalidProduct.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(validHolder.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returnsMany listOf(
            holderBeneficiary.success(),
            invalidDateHolderBeneficiary.success()
        )

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1, index3, index5), result.success)
        assertEquals(2, result.errors.size)

        val productError = result.errors.find { it.index == index2 }
        assertEquals("productTitle", productError?.error?.first()?.field)
        assertEquals("Produto não encontrado.", productError?.error?.first()?.message)

        val dateError = result.errors.find { it.index == index4 }
        assertEquals("activatedAt", dateError?.error?.first()?.field)
        assertEquals("Data de ativação deve ser igual ou posterior à contratação do titular.", dateError?.error?.first()?.message)
    }

    @Test
    fun `run should handle duplicate product titles in batch`() = runBlocking {
        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
            index = index1,
            productTitle = "Plano de Saúde Bonzao"
        )
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
            index = index2,
            productTitle = "Plano de Saúde Bonzao"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2)
        )

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiary1.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiary1.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1, index2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle mixed null and valid product titles`() = runBlocking {
        val beneficiaryWithValidProduct = beneficiaryBatchItemTransportValid.copy(
            index = index1,
            productTitle = "Plano de Saúde Bonzao"
        )
        val beneficiaryWithNullProduct = beneficiaryBatchItemTransportValid.copy(
            index = index2,
            productTitle = null
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryWithValidProduct, beneficiaryWithNullProduct)
        )

        coEvery { personService.findByNationalId(beneficiaryWithValidProduct.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryWithValidProduct.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto não encontrado."
                )
            )
        )

        assertEquals(listOf(index1), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate product is not blocked for sale`() = runBlocking {
        val blockedPriceListing = companyProductPriceListing.copy(isBlockedForSale = true)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns blockedPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto inválido, use a lista da planilha modelo."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate product is available in subcontract`() = runBlocking {
        val subContractWithoutProduct = companySubContract.copy(availableProducts = emptyList())
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(subContractWithoutProduct).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto inválido, use a lista da planilha modelo."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate holder has required cnpj information`() = runBlocking {
        val holderWithoutCnpj = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = null,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderWithoutCnpj.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate holder has required contract type information`() = runBlocking {
        val holderWithoutContractType = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = null,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderWithoutContractType.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate holder has required hired date information`() = runBlocking {
        val holderWithoutHiredAt = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = null,
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderWithoutHiredAt.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate holder belongs to same company`() = runBlocking {
        val differentCompanyId = RangeUUID.generate()
        val holderFromDifferentCompany = TestModelFactory.buildBeneficiary(
            companyId = differentCompanyId,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderFromDifferentCompany.success()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009") } returns NotFoundException().failure()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate activation date is after company contract start`() = runBlocking {
        val companyWithLateContract = company.copy(
            contractStartedAt = localDateTime.plusDays(10)
        )
        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(companyWithLateContract, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "activatedAt",
                    message = "Data de ativação deve ser após o início do contrato com a Alice."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle missing subcontract for beneficiary`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns emptyList<CompanySubContract>().success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "subContractTitle",
                    message = "Contrato para faturamento inválido, selecione uma das opções da lista.",
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(expectedError, result.errors.first())
    }

    @Test
    fun `run should handle missing billing accountable party`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should return error when person with national id exists and member is active from other company`() = runBlocking {
        val otherCompanyId = RangeUUID.generate()
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val activeMemberFromOtherCompany = TestModelFactory.buildMember(
            personId = existingPerson.id,
            companyId = otherCompanyId,
            status = MemberStatus.ACTIVE
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns activeMemberFromOtherCompany.success()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "nationalId",
                    message = "CPF já possui um plano ativo na Alice, fale com o time de apoio."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should return error when person with national id exists and member is pending from other company`() = runBlocking {
        val otherCompanyId = RangeUUID.generate()
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val pendingMemberFromOtherCompany = TestModelFactory.buildMember(
            personId = existingPerson.id,
            companyId = otherCompanyId,
            status = MemberStatus.PENDING
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns pendingMemberFromOtherCompany.success()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "nationalId",
                    message = "CPF já possui um cadastro na Alice, fale com o time de apoio."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should succeed when person with national id exists and member is from same company`() = runBlocking {
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val memberFromSameCompany = TestModelFactory.buildMember(
            personId = existingPerson.id,
            companyId = companyId,
            status = MemberStatus.ACTIVE
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns memberFromSameCompany.success()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should succeed when person with national id exists and member is canceled from other company`() = runBlocking {
        val otherCompanyId = RangeUUID.generate()
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val canceledMemberFromOtherCompany = TestModelFactory.buildMember(
            personId = existingPerson.id,
            companyId = otherCompanyId,
            status = MemberStatus.CANCELED
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns canceledMemberFromOtherCompany.success()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should succeed when person with national id exists but member service returns not found`() = runBlocking {
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle multiple beneficiaries with mixed national id validation scenarios`() = runBlocking {
        val otherCompanyId = RangeUUID.generate()
        val existingPerson1 = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val existingPerson2 = TestModelFactory.buildPerson(nationalId = "123.456.789-00")

        val activeMemberFromOtherCompany = TestModelFactory.buildMember(
            personId = existingPerson1.id,
            companyId = otherCompanyId,
            status = MemberStatus.ACTIVE
        )
        val memberFromSameCompany = TestModelFactory.buildMember(
            personId = existingPerson2.id,
            companyId = companyId,
            status = MemberStatus.ACTIVE
        )

        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(index = index1)
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
            index = index2,
            nationalId = "123.456.789-00"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2)
        )

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns existingPerson1.success()
        coEvery { memberService.getCurrent(existingPerson1.id) } returns activeMemberFromOtherCompany.success()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns existingPerson2.success()
        coEvery { memberService.getCurrent(existingPerson2.id) } returns memberFromSameCompany.success()
        coEvery { productService.findActiveByTitles(listOf(beneficiary1.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiary1.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "nationalId",
                    message = "CPF já possui um plano ativo na Alice, fale com o time de apoio."
                )
            )
        )

        assertEquals(listOf(index2), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle member service throwing unexpected exception`() = runBlocking {
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns RuntimeException("Database error").failure()

        assertFailsWith<RuntimeException> {
            dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        }
    }

    @Test
    fun `run should handle person service throwing unexpected exception`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns RuntimeException("Database error").failure()

        assertFailsWith<RuntimeException> {
            dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        }
    }

    @Test
    fun `run should validate national id with different member statuses from other company`() = runBlocking {
        val otherCompanyId = RangeUUID.generate()
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)

        val pendingMemberFromOtherCompany = TestModelFactory.buildMember(
            personId = existingPerson.id,
            companyId = otherCompanyId,
            status = MemberStatus.PENDING
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns pendingMemberFromOtherCompany.success()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "nationalId",
                    message = "CPF já possui um cadastro na Alice, fale com o time de apoio."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should succeed when person exists but member is null`() = runBlocking {
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle empty subcontract title list`() = runBlocking {
        val beneficiaryWithoutSubcontractTitle = beneficiaryBatchItemTransportValid.copy(
            subContractTitle = null,
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryWithoutSubcontractTitle)
        )

        coEvery { personService.findByNationalId(beneficiaryWithoutSubcontractTitle.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryWithoutSubcontractTitle.productTitle!!)) } returns listOf(product).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "subContractTitle",
                    message = "Contrato para faturamento inválido, selecione uma das opções da lista."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(expectedError, result.errors.first())
    }

    @Test
    fun `run should handle dependent with holder in same batch`() = runBlocking {
        val holderInBatch = beneficiaryBatchItemTransportValid.copy(
            index = index1,
            nationalId = "438.595.570-09",
            parentNationalId = null
        )
        val dependentInBatch = dependentBeneficiaryBatchItemTransportValid.copy(
            index = index2,
            parentNationalId = "438.595.570-09"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(holderInBatch, dependentInBatch)
        )

        coEvery { personService.findByNationalId(holderInBatch.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(dependentInBatch.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(holderInBatch.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns NotFoundException().failure()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1, index2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle service failures gracefully`(): Unit = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(any()) } returns RuntimeException("Service unavailable").failure()

        assertFailsWith<RuntimeException> {
            dynamicValidateBeneficiariesUseCase.run(company, transport)
        }
    }

    @Test
    fun `run should handle companySubContractService failure`(): Unit = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(any()) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(any()) } returns RuntimeException("Database error").failure()

        assertFailsWith<RuntimeException> {
            dynamicValidateBeneficiariesUseCase.run(company, transport)
        }
    }

    @Test
    fun `run should handle companyProductPriceListingService failure`(): Unit = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(any()) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(any()) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(any(), any())
        } returns RuntimeException("Pricing service error").failure()

        assertFailsWith<RuntimeException> {
            dynamicValidateBeneficiariesUseCase.run(company, transport)
        }
    }

    @Test
    fun `run should handle personService failure for dependent validation`(): Unit = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(any()) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(any()) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(any(), any())
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId(any()) } returns RuntimeException("Person service error").failure()

        assertFailsWith<RuntimeException> {
            dynamicValidateBeneficiariesUseCase.run(company, transport)
        }
    }

    @Test
    fun `run should handle beneficiaryService failure for dependent validation`(): Unit = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(any()) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(any()) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(any(), any())
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId(any()) } returns person.success()
        coEvery { beneficiaryService.findByPersonId(any()) } returns RuntimeException("Beneficiary service error").failure()

        assertFailsWith<RuntimeException> {
            dynamicValidateBeneficiariesUseCase.run(company, transport)
        }
    }

    @Test
    fun `run should handle combination of product blocked and product not available errors`() = runBlocking {
        val blockedPriceListing = companyProductPriceListing.copy(isBlockedForSale = true)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(any(), any())
        } returns blockedPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto inválido, use a lista da planilha modelo."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle price listing service returning null`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns NotFoundException().failure()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle price listing with isBlockedForSale null`() = runBlocking {
        val priceListingWithNullBlocked = companyProductPriceListing.copy(isBlockedForSale = null)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns priceListingWithNullBlocked.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle subcontract with null available products`() = runBlocking {
        val subContractWithNullProducts = companySubContract.copy(availableProducts = null)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(subContractWithNullProducts).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(subContractWithNullProducts.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        val expectedError = BeneficiaryBatchValidationError(
            index = index1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto inválido, use a lista da planilha modelo."
                )
            )
        )
        
        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should find on tracking table for holder beneficiary when it is not on database neither on batch`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )
        val hrMemberUploadTracking = TestModelFactory.buildHrMemberUploadTracking(
            memberNationalId = "43859557009",
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns NotFoundException().failure()
        coEvery {
            hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009")
        } returns hrMemberUploadTracking.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should return an error when beneficiary is not queued or create on tracking table`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )
        val hrMemberUploadTracking = TestModelFactory.buildHrMemberUploadTracking(
            memberNationalId = "43859557009",
            status = ERROR,
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns NotFoundException().failure()
        coEvery {
            hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009")
        } returns hrMemberUploadTracking.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should return null product and subcontract title options when no items provided`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(items = emptyList())

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(null, result.productTitleOptions)
        assertEquals(null, result.subContractTitleOptions)
        assertEquals(emptyList<Int>(), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should return distinct and sorted product title options`() = runBlocking {
        val product1 = TestModelFactory.buildProduct(title = "Plano Z")
        val product2 = TestModelFactory.buildProduct(title = "Plano A")
        val product3 = TestModelFactory.buildProduct(title = "Plano B")

        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), productTitle = "Plano Z")
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), productTitle = "Plano A")
        val beneficiary3 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), productTitle = "Plano B")
        val beneficiary4 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), productTitle = "Plano A")

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2, beneficiary3, beneficiary4)
        )

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary3.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary4.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf("Plano Z", "Plano A", "Plano B")) } returns listOf(product1, product2, product3).success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz")) } returns listOf(companySubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf("Plano A", "Plano B", "Plano Z"), result.productTitleOptions)
        assertEquals(listOf("Matriz"), result.subContractTitleOptions)
    }

    @Test
    fun `run should return distinct and sorted subcontract title options`() = runBlocking {
        val subContract1 = companySubContract.copy(title = "Contrato Z")
        val subContract2 = companySubContract.copy(title = "Contrato A")
        val subContract3 = companySubContract.copy(title = "Contrato B")

        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), subContractTitle = "Contrato Z")
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), subContractTitle = "Contrato A")
        val beneficiary3 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), subContractTitle = "Contrato B")
        val beneficiary4 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), subContractTitle = "Contrato A")

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2, beneficiary3, beneficiary4)
        )

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary3.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary4.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf("Plano de Saúde Bonzao")) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf("Contrato Z", "Contrato A", "Contrato B")) } returns listOf(subContract1, subContract2, subContract3).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(any(), any())
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()


        assertEquals(listOf("Plano de Saúde Bonzao"), result.productTitleOptions)
        assertEquals(listOf("Contrato A", "Contrato B", "Contrato Z"), result.subContractTitleOptions)
    }

    @Test
    fun `run should handle null products and subcontracts in title options`() = runBlocking {
        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), productTitle = "Existing Product")
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), productTitle = "Non-existing Product")
        val beneficiary3 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), subContractTitle = "Existing Contract")
        val beneficiary4 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), subContractTitle = "Non-existing Contract")

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2, beneficiary3, beneficiary4)
        )

        val existingProduct = TestModelFactory.buildProduct(title = "Existing Product")
        val existingSubContract = companySubContract.copy(title = "Existing Contract")

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary3.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary4.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf("Existing Product", "Non-existing Product", "Plano de Saúde Bonzao")) } returns listOf(existingProduct).success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz", "Existing Contract", "Non-existing Contract")) } returns listOf(existingSubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf("Existing Product"), result.productTitleOptions)
        assertEquals(listOf("Existing Contract"), result.subContractTitleOptions)
        assertTrue(result.errors.isNotEmpty())
    }

    @Test
    fun `run should return empty title options when all products and subcontracts are null`() = runBlocking {
        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), productTitle = "Non-existing Product")
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(index = RangeUUID.generate(), productTitle = "Another Non-existing Product", subContractTitle = "Non-existing Contract")

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2)
        )

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf("Non-existing Product", "Another Non-existing Product")) } returns emptyList<Product>().success()
        coEvery { companySubContractService.findByTitles(listOf("Matriz", "Non-existing Contract")) } returns emptyList<CompanySubContract>().success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(emptyList<String>(), result.productTitleOptions)
        assertEquals(emptyList<String>(), result.subContractTitleOptions)
        assertTrue(result.errors.isNotEmpty())
    }

    @Test
    fun `run should handle mixed valid and invalid items with correct title options`() = runBlocking {
        val validProduct = TestModelFactory.buildProduct(title = "Valid Product")
        val validSubContract = companySubContract.copy(title = "Valid Contract")

        val validBeneficiary = beneficiaryBatchItemTransportValid.copy(
            index = RangeUUID.generate(),
            productTitle = "Valid Product",
            subContractTitle = "Valid Contract"
        )
        val invalidProductBeneficiary = beneficiaryBatchItemTransportValid.copy(
            index = RangeUUID.generate(),
            productTitle = "Invalid Product",
            subContractTitle = "Valid Contract"
        )
        val invalidSubContractBeneficiary = beneficiaryBatchItemTransportValid.copy(
            index = RangeUUID.generate(),
            productTitle = "Valid Product",
            subContractTitle = "Invalid Contract"
        )

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(validBeneficiary, invalidProductBeneficiary, invalidSubContractBeneficiary)
        )

        coEvery { personService.findByNationalId(validBeneficiary.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(invalidProductBeneficiary.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(invalidSubContractBeneficiary.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf("Valid Product", "Invalid Product")) } returns listOf(validProduct).success()
        coEvery { companySubContractService.findByTitles(listOf("Valid Contract", "Invalid Contract")) } returns listOf(validSubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf("Valid Product"), result.productTitleOptions)
        assertEquals(listOf("Valid Contract"), result.subContractTitleOptions)
        assertTrue(result.errors.isNotEmpty())
    }

    @Test
    fun `run should handle invalid date format in dependent activation date`() = runBlocking {
        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val dependentWithInvalidDate = dependentBeneficiaryBatchItemTransportValid.copy(
            activatedAt = "32/01/2023"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentWithInvalidDate)
        )

        coEvery { personService.findByNationalId(dependentWithInvalidDate.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentWithInvalidDate.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentWithInvalidDate.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        assertFailsWith<Exception> {
            dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        }
    }

    @Test
    fun `run should handle malformed date with wrong separators in holder from batch`() = runBlocking {
        val holderInBatch = beneficiaryBatchItemTransportValid.copy(
            index = index1,
            activatedAt = "01.01.2023",
            hiredAt = "01.01.2023"
        )
        val dependentItem = dependentBeneficiaryBatchItemTransportValid.copy(
            index = index2,
            parentNationalId = holderInBatch.nationalId
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(holderInBatch, dependentItem)
        )

        coEvery { personService.findByNationalId(holderInBatch.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(dependentItem.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(holderInBatch.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(holderInBatch.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns NotFoundException().failure()

        assertFailsWith<Exception> {
            dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        }
    }

    @Test
    fun `run should handle leap year date validation correctly`() = runBlocking {
        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        // Use a date that's after company contract start but before current date
        val validDate = localDateTime.minusDays(10).toLocalDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        val dependentWithLeapYearDate = dependentBeneficiaryBatchItemTransportValid.copy(
            activatedAt = validDate
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentWithLeapYearDate)
        )

        coEvery { personService.findByNationalId(dependentWithLeapYearDate.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentWithLeapYearDate.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentWithLeapYearDate.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle invalid leap year date`() = runBlocking {
        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val dependentWithInvalidLeapYearDate = dependentBeneficiaryBatchItemTransportValid.copy(
            activatedAt = "29/02/2023"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentWithInvalidLeapYearDate)
        )

        coEvery { personService.findByNationalId(dependentWithInvalidLeapYearDate.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentWithInvalidLeapYearDate.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentWithInvalidLeapYearDate.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        assertFailsWith<Exception> {
            dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        }
    }

    @Test
    fun `run should handle member with canceled status from different company allowing new registration`() = runBlocking {
        val otherCompanyId = RangeUUID.generate()
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val canceledMemberFromOtherCompany = TestModelFactory.buildMember(
            personId = existingPerson.id,
            companyId = otherCompanyId,
            status = MemberStatus.CANCELED
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns canceledMemberFromOtherCompany.success()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle multiple validation errors on single beneficiary with national id conflict`() = runBlocking {
        val otherCompanyId = RangeUUID.generate()
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val activeMemberFromOtherCompany = TestModelFactory.buildMember(
            personId = existingPerson.id,
            companyId = otherCompanyId,
            status = MemberStatus.ACTIVE
        )
        val beneficiaryWithMultipleIssues = beneficiaryBatchItemTransportValid.copy(
            productTitle = "Non-existent Product"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryWithMultipleIssues)
        )

        coEvery { personService.findByNationalId(beneficiaryWithMultipleIssues.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns activeMemberFromOtherCompany.success()
        coEvery { productService.findActiveByTitles(listOf("Non-existent Product")) } returns emptyList<Product>().success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryWithMultipleIssues.subContractTitle!!)) } returns listOf(companySubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(2, result.errors.size)

        // The validation logic accumulates multiple errors per beneficiary
        // First error should be the national ID error
        val nationalIdError = result.errors.find { error ->
            error.error.any { it.field == "nationalId" }
        }
        assertNotNull(nationalIdError)
        assertEquals(index1, nationalIdError!!.index)
        assertEquals("nationalId", nationalIdError.error.first().field)
        assertEquals("CPF já possui um plano ativo na Alice, fale com o time de apoio.", nationalIdError.error.first().message)

        // Second error should be the product error
        val productError = result.errors.find { error ->
            error.error.any { it.field == "productTitle" }
        }
        assertNotNull(productError)
        assertEquals(index1, productError!!.index)
        assertEquals("productTitle", productError.error.first().field)
        assertEquals("Produto não encontrado.", productError.error.first().message)
    }

    @Test
    fun `run should handle person exists but member is explicitly null`() = runBlocking {
        val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
        coEvery { memberService.getCurrent(existingPerson.id) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle hrMemberUploadTrackingDataService throwing exception`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns NotFoundException().failure()
        coEvery {
            hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009")
        } returns RuntimeException("Tracking service error").failure()

        assertFailsWith<RuntimeException> {
            dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        }
    }

    @Test
    fun `run should handle tracking service with queued status allowing dependent validation`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )
        val queuedTracking = TestModelFactory.buildHrMemberUploadTracking(
            memberNationalId = "43859557009",
            status = QUEUED
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns NotFoundException().failure()
        coEvery {
            hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009")
        } returns queuedTracking.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle tracking service with created status allowing dependent validation`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )
        val createdTracking = TestModelFactory.buildHrMemberUploadTracking(
            memberNationalId = "43859557009",
            status = CREATED
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns NotFoundException().failure()
        coEvery {
            hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009")
        } returns createdTracking.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle partial service failures during batch processing`() = runBlocking {
        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(index = index1)
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
            index = index2,
            nationalId = "123.456.789-00"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2)
        )

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns RuntimeException("Service error").failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiary1.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiary1.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        assertFailsWith<RuntimeException> {
            dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        }
    }

    @Test
    fun `run should handle all beneficiaries with null product titles`() = runBlocking {
        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
            index = index1,
            productTitle = null
        )
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
            index = index2,
            productTitle = null
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2)
        )

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(emptyList()) } returns emptyList<Product>().success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiary1.subContractTitle!!)) } returns listOf(companySubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(2, result.errors.size)
        result.errors.forEach { error ->
            assertEquals("productTitle", error.error.first().field)
            assertEquals("Produto não encontrado.", error.error.first().message)
        }
    }

    @Test
    fun `run should handle all beneficiaries with null subcontract titles`() = runBlocking {
        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
            index = index1,
            subContractTitle = null
        )
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
            index = index2,
            subContractTitle = null
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2)
        )

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiary1.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(emptyList()) } returns emptyList<CompanySubContract>().success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(2, result.errors.size)
        result.errors.forEach { error ->
            assertEquals("subContractTitle", error.error.first().field)
            assertEquals("Contrato para faturamento inválido, selecione uma das opções da lista.", error.error.first().message)
        }
    }

    @Test
    fun `run should handle mixed null and valid product titles correctly`() = runBlocking {
        val validBeneficiary = beneficiaryBatchItemTransportValid.copy(
            index = index1,
            productTitle = product.title
        )
        val nullProductBeneficiary = beneficiaryBatchItemTransportValid.copy(
            index = index2,
            productTitle = null
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(validBeneficiary, nullProductBeneficiary)
        )

        coEvery { personService.findByNationalId(validBeneficiary.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(nullProductBeneficiary.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(product.title)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(validBeneficiary.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index1), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(index2, result.errors[0].index)
        assertEquals("productTitle", result.errors[0].error.first().field)
        assertEquals("Produto não encontrado.", result.errors[0].error.first().message)
    }

    @Test
    fun `run should skip product availability validation when product error exists`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns emptyList<Product>().success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals("productTitle", result.errors[0].error.first().field)
        assertEquals("Produto não encontrado.", result.errors[0].error.first().message)
    }

    @Test
    fun `run should skip product availability validation when subcontract error exists`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns emptyList<CompanySubContract>().success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals("subContractTitle", result.errors[0].error.first().field)
        assertEquals("Contrato para faturamento inválido, selecione uma das opções da lista.", result.errors[0].error.first().message)
    }

    @Test
    fun `run should skip product blocking validation when product availability error exists`() = runBlocking {
        val subContractWithoutProduct = companySubContract.copy(availableProducts = emptyList())
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(subContractWithoutProduct).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals("productTitle", result.errors[0].error.first().field)
        assertEquals("Produto inválido, use a lista da planilha modelo.", result.errors[0].error.first().message)
    }

    @Test
    fun `run should execute product blocking validation only when no previous errors exist`() = runBlocking {
        val blockedPriceListing = companyProductPriceListing.copy(isBlockedForSale = true)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns blockedPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals("productTitle", result.errors[0].error.first().field)
        assertEquals("Produto inválido, use a lista da planilha modelo.", result.errors[0].error.first().message)
    }

    @Test
    fun `run should handle validation short-circuiting with multiple beneficiaries`() = runBlocking {
        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
            index = index1,
            productTitle = "Non-existent Product"
        )
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
            index = index2,
            productTitle = product.title // Use existing product that's in availableProducts
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2)
        )

        coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
        coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf("Non-existent Product", product.title)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiary1.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index2), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(index1, result.errors[0].index)
        assertEquals("productTitle", result.errors[0].error.first().field)
        assertEquals("Produto não encontrado.", result.errors[0].error.first().message)
    }

    @Test
    fun `run should handle tracking status QUEUED for holder validation`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )
        val memberTracking = TestModelFactory.buildHrMemberUploadTracking(
            memberNationalId = "43859557009",
            status = QUEUED
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns NotFoundException().failure()
        coEvery { beneficiaryService.findByPersonId(any()) } returns NotFoundException().failure()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009") } returns memberTracking.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle tracking status CREATED for holder validation`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )
        val memberTracking = TestModelFactory.buildHrMemberUploadTracking(
            memberNationalId = "43859557009",
            status = CREATED
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns NotFoundException().failure()
        coEvery { beneficiaryService.findByPersonId(any()) } returns NotFoundException().failure()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009") } returns memberTracking.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(index2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle tracking status ERROR for holder validation`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )
        val memberTracking = TestModelFactory.buildHrMemberUploadTracking(
            memberNationalId = "43859557009",
            status = ERROR
        )

        coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns NotFoundException().failure()
        coEvery { beneficiaryService.findByPersonId(any()) } returns NotFoundException().failure()
        coEvery { hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009") } returns memberTracking.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = index2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<UUID>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Nested
    inner class NationalIdValidationTests {
        @Test
        fun `run should handle null national id in validation`() = runBlocking {
            val beneficiaryWithNullNationalId = beneficiaryBatchItemTransportValid.copy(
                nationalId = null
            )
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryWithNullNationalId)
            )
            val staticValidation = BeneficiaryBatchValidation(
                errors = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "nationalId",
                                message = "CPF obrigatório."
                            )
                        )
                    )
                )
            )

            coEvery { productService.findActiveByTitles(listOf(beneficiaryWithNullNationalId.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryWithNullNationalId.subContractTitle!!)) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns companyProductPriceListing.success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport, staticValidation).get()

            assertEquals(listOf(index1), result.success)
            assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
        }

        @Test
        fun `run should handle person service error during national id validation`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns RuntimeException("Service error").failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()

            assertFailsWith<RuntimeException> {
                dynamicValidateBeneficiariesUseCase.run(company, transport).get()
            }
        }

        @Test
        fun `run should handle member service error during national id validation`() = runBlocking {
            val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
            coEvery { memberService.getCurrent(existingPerson.id) } returns RuntimeException("Member service error").failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()

            assertFailsWith<RuntimeException> {
                dynamicValidateBeneficiariesUseCase.run(company, transport).get()
            }
        }

        @Test
        fun `run should succeed when person exists but member is from same company`() = runBlocking {
            val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
            val memberFromSameCompany = TestModelFactory.buildMember(
                companyId = companyId,
                status = MemberStatus.ACTIVE
            )
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
            coEvery { memberService.getCurrent(existingPerson.id) } returns memberFromSameCompany.success()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns companyProductPriceListing.success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(listOf(index1), result.success)
            assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
        }

        @Test
        fun `run should handle inactive member from other company`() = runBlocking {
            val otherCompanyId = RangeUUID.generate()
            val existingPerson = TestModelFactory.buildPerson(nationalId = beneficiaryBatchItemTransportValid.nationalId!!)
            val inactiveMemberFromOtherCompany = TestModelFactory.buildMember(
                companyId = otherCompanyId,
                status = MemberStatus.CANCELED
            )
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns existingPerson.success()
            coEvery { memberService.getCurrent(existingPerson.id) } returns inactiveMemberFromOtherCompany.success()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns companyProductPriceListing.success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(listOf(index1), result.success)
            assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
        }
    }

    @Nested
    inner class ServiceIntegrationErrorTests {
        @Test
        fun `run should handle product service error`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns RuntimeException("Product service error").failure()

            assertFailsWith<RuntimeException> {
                dynamicValidateBeneficiariesUseCase.run(company, transport).get()
            }
        }

        @Test
        fun `run should handle subcontract service error`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns RuntimeException("Subcontract service error").failure()

            assertFailsWith<RuntimeException> {
                dynamicValidateBeneficiariesUseCase.run(company, transport).get()
            }
        }

        @Test
        fun `run should handle price listing service error`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns RuntimeException("Price listing service error").failure()

            assertFailsWith<RuntimeException> {
                dynamicValidateBeneficiariesUseCase.run(company, transport).get()
            }
        }

        @Test
        fun `run should handle beneficiary service error during holder validation`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(dependentBeneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns companyProductPriceListing.success()
            coEvery { personService.findByNationalId("43859557009") } returns person.success()
            coEvery { beneficiaryService.findByPersonId(person.id) } returns RuntimeException("Beneficiary service error").failure()

            assertFailsWith<RuntimeException> {
                dynamicValidateBeneficiariesUseCase.run(company, transport).get()
            }
        }

        @Test
        fun `run should handle tracking service error during holder validation`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(dependentBeneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(dependentBeneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns companyProductPriceListing.success()
            coEvery { personService.findByNationalId("43859557009") } returns person.success()
            coEvery { beneficiaryService.findByPersonId(person.id) } returns NotFoundException().failure()
            coEvery { hrMemberUploadTrackingDataService.findByMemberNationalId("43859557009") } returns RuntimeException("Tracking service error").failure()

            assertFailsWith<RuntimeException> {
                dynamicValidateBeneficiariesUseCase.run(company, transport).get()
            }
        }
    }

    @Nested
    inner class StaticValidationIntegrationTests {
        @Test
        fun `run should skip product validation when already validated by static validation`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )
            val staticValidation = BeneficiaryBatchValidation(
                errors = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "productTitle",
                                message = "Static validation error"
                            )
                        )
                    )
                )
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(any()) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns companyProductPriceListing.success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport, staticValidation).get()

            assertEquals(listOf(index1), result.success)
            assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
        }

        @Test
        fun `run should skip national id validation when already validated by static validation`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )
            val staticValidation = BeneficiaryBatchValidation(
                errors = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "nationalId",
                                message = "Static validation error"
                            )
                        )
                    )
                )
            )

            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns companyProductPriceListing.success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport, staticValidation).get()

            assertEquals(listOf(index1), result.success)
            assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
        }

        @Test
        fun `run should skip subcontract validation when already validated by static validation`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )
            val staticValidation = BeneficiaryBatchValidation(
                errors = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "subContractTitle",
                                message = "Static validation error"
                            )
                        )
                    )
                )
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(any()) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns companyProductPriceListing.success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport, staticValidation).get()

            assertEquals(listOf(index1), result.success)
            assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
        }

        @Test
        fun `run should perform validation when different field has static validation error`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )
            val staticValidation = BeneficiaryBatchValidation(
                errors = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "email",
                                message = "Static validation error"
                            )
                        )
                    )
                )
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns emptyList<Product>().success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport, staticValidation).get()

            assertEquals(emptyList<UUID>(), result.success)
            assertEquals(1, result.errors.size)
            assertEquals("productTitle", result.errors[0].error[0].field)
        }
    }

    @Nested
    inner class BatchValidationBehaviorTests {
        @Test
        fun `run should handle multiple validation errors for single beneficiary`() = runBlocking {
            val beneficiaryWithMultipleErrors = beneficiaryBatchItemTransportValid.copy(
                productTitle = "Non-existing Product"
            )
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryWithMultipleErrors)
            )

            coEvery { personService.findByNationalId(beneficiaryWithMultipleErrors.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryWithMultipleErrors.productTitle!!)) } returns emptyList<Product>().success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryWithMultipleErrors.subContractTitle!!)) } returns emptyList<CompanySubContract>().success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(emptyList<UUID>(), result.success)
            assertEquals(2, result.errors.size)
            assertTrue(result.errors.any { it.error.any { error -> error.field == "productTitle" } })
            assertTrue(result.errors.any { it.error.any { error -> error.field == "subContractTitle" } })
        }

        @Test
        fun `run should not validate product availability when product validation fails`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns emptyList<Product>().success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(emptyList<UUID>(), result.success)
            assertEquals(1, result.errors.size)
            assertEquals("productTitle", result.errors[0].error[0].field)
            assertEquals("Produto não encontrado.", result.errors[0].error[0].message)
        }

        @Test
        fun `run should not validate product blocking when subcontract validation fails`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns emptyList<CompanySubContract>().success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(emptyList<UUID>(), result.success)
            assertEquals(1, result.errors.size)
            assertEquals("subContractTitle", result.errors[0].error[0].field)
        }

        @Test
        fun `run should return correct product and subcontract title options`() = runBlocking {
            val product2 = TestModelFactory.buildProduct(title = "Another Product")
            val subContract2 = TestModelFactory.buildCompanySubContract(
                title = "Another Contract",
                companyId = companyId,
                availableProducts = listOf(product2.id)
            )
            val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                productTitle = product.title,
                subContractTitle = companySubContract.title
            )
            val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
                index = index2,
                productTitle = product2.title,
                subContractTitle = subContract2.title
            )
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiary1, beneficiary2)
            )

            coEvery { personService.findByNationalId(beneficiary1.nationalId!!) } returns NotFoundException().failure()
            coEvery { personService.findByNationalId(beneficiary2.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(product.title, product2.title)) } returns listOf(product, product2).success()
            coEvery { companySubContractService.findByTitles(listOf(companySubContract.title, subContract2.title)) } returns listOf(companySubContract, subContract2).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns companyProductPriceListing.success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(subContract2.id, product2.id)
            } returns TestModelFactory.buildCompanyProductPriceListing(
                productId = product2.id,
                companySubContractId = subContract2.id
            ).copy(isBlockedForSale = false).success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(listOf(index1, index2), result.success)
            assertEquals(listOf("Another Product", "Plano de Saúde Bonzao"), result.productTitleOptions)
            assertEquals(listOf("Another Contract", "Matriz"), result.subContractTitleOptions)
        }

        @Test
        fun `run should handle empty product and subcontract lists gracefully`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns emptyList<Product>().success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns emptyList<CompanySubContract>().success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(emptyList<String>(), result.productTitleOptions)
            assertEquals(emptyList<String>(), result.subContractTitleOptions)
            assertEquals(emptyList<UUID>(), result.success)
            assertTrue(result.errors.isNotEmpty())
        }
    }

    @Nested
    inner class EdgeCaseTests {
        @Test
        fun `run should handle beneficiary with null product title`() = runBlocking {
            val beneficiaryWithNullProduct = beneficiaryBatchItemTransportValid.copy(
                productTitle = null
            )
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryWithNullProduct)
            )

            coEvery { personService.findByNationalId(beneficiaryWithNullProduct.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(emptyList()) } returns emptyList<Product>().success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryWithNullProduct.subContractTitle!!)) } returns listOf(companySubContract).success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(emptyList<UUID>(), result.success)
            assertEquals(1, result.errors.size)
            assertEquals("productTitle", result.errors[0].error[0].field)
        }

        @Test
        fun `run should handle beneficiary with null subcontract title`() = runBlocking {
            val beneficiaryWithNullSubcontract = beneficiaryBatchItemTransportValid.copy(
                subContractTitle = null
            )
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryWithNullSubcontract)
            )

            coEvery { personService.findByNationalId(beneficiaryWithNullSubcontract.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryWithNullSubcontract.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(emptyList()) } returns emptyList<CompanySubContract>().success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(emptyList<UUID>(), result.success)
            assertEquals(1, result.errors.size)
            assertEquals("subContractTitle", result.errors[0].error[0].field)
        }

        @Test
        fun `run should handle subcontract with null available products`() = runBlocking {
            val subContractWithNullProducts = companySubContract.copy(availableProducts = null)
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(subContractWithNullProducts).success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(emptyList<UUID>(), result.success)
            assertEquals(1, result.errors.size)
            assertEquals("productTitle", result.errors[0].error[0].field)
            assertEquals("Produto inválido, use a lista da planilha modelo.", result.errors[0].error[0].message)
        }

        @Test
        fun `run should handle price listing service returning null`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryBatchItemTransportValid)
            )

            coEvery { personService.findByNationalId(beneficiaryBatchItemTransportValid.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns NotFoundException().failure()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(listOf(index1), result.success)
            assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
        }

        @Test
        fun `run should handle dependent with null parent national id`() = runBlocking {
            val dependentWithNullParent = dependentBeneficiaryBatchItemTransportValid.copy(
                parentNationalId = null
            )
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(dependentWithNullParent)
            )

            coEvery { personService.findByNationalId(dependentWithNullParent.nationalId!!) } returns NotFoundException().failure()
            coEvery { productService.findActiveByTitles(listOf(dependentWithNullParent.productTitle!!)) } returns listOf(product).success()
            coEvery { companySubContractService.findByTitles(listOf(dependentWithNullParent.subContractTitle!!)) } returns listOf(companySubContract).success()
            coEvery {
                companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
            } returns companyProductPriceListing.success()

            val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

            assertEquals(listOf(index2), result.success)
            assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
        }
    }
}
