plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.dbintegrationservice"
version = aliceDBIntegrationServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        java.sourceDirs = files("src/main/java", "${layout.buildDirectory}/generated/source/java")
        kotlin.sourceDirs = files("src", "src/main/java", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:db-integration-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-kafka"))
    implementation(project(":common-service"))
    implementation(project(":membership-domain-client"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:provider-domain-service-data-package"))
	implementation(project(":data-packages:db-integration-service-data-package"))
	implementation(project(":data-packages:exec-indicator-domain-service-data-package"))
    implementation(project(":provider-domain-client"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":db-integration-client"))
    implementation(project(":exec-indicator-domain-client"))
    implementation(project(":person-domain-client"))

    ktor2Dependencies()

    implementation("io.ktor:ktor-client-apache5:$ktor2Version")
    implementation("com.sun.xml.bind:jaxb-ri:2.3.3")  //java 11 JAXB
    implementation("com.sun.xml.ws:jaxws-ri:2.3.2") //java 11 JAXWS
    implementation("com.google.firebase:firebase-admin:$firebaseAdminVersion")

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))
    test2Dependencies()
}
